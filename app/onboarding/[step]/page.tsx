'use client';

import React, { useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { OnboardingProvider, useOnboarding } from '@/components/organisms/OnboardingWizard/OnboardingContext';
import OnboardingWizard from '@/components/organisms/OnboardingWizard/OnboardingWizard';
import { OnboardingErrorBoundary } from '@/components/organisms/OnboardingWizard/OnboardingErrorBoundary';
import SchoolDetailsStep from '@/components/organisms/OnboardingWizard/SchoolDetailsStep';
import BrandingStep from '@/components/organisms/OnboardingWizard/BrandingStep';
import ProfileStep from '@/components/organisms/OnboardingWizard/ProfileStep';
import CompletionStep from '@/components/organisms/OnboardingWizard/CompletionStep';
import { EUserRole } from '@/config/enums/user';

const VALID_STEPS = ['school-details', 'branding', 'profile', 'complete'];

function OnboardingStepContent() {
  const params = useParams();
  const router = useRouter();
  const { setCurrentStep, canNavigateToStep } = useOnboarding();
  const step = params.step as string;

  useEffect(() => {
    // Validate step parameter
    if (!VALID_STEPS.includes(step)) {
      console.warn(`Invalid onboarding step: ${step}. Redirecting to school-details.`);
      router.replace('/onboarding/school-details');
      return;
    }

    // Check if user can navigate to this step
    if (!canNavigateToStep(step)) {
      console.warn(`Cannot navigate to step: ${step}. Redirecting to school-details.`);
      // Redirect to the first incomplete step
      router.replace('/onboarding/school-details');
      return;
    }

    // Set current step in context
    setCurrentStep(step);
  }, [step]); // Only depend on step parameter, functions are now memoized

  const renderStepContent = () => {
    switch (step) {
      case 'school-details':
        return <SchoolDetailsStep />;
      case 'branding':
        return <BrandingStep />;
      case 'profile':
        return <ProfileStep />;
      case 'complete':
        return <CompletionStep />;
      default:
        return <SchoolDetailsStep />;
    }
  };

  return (
    <OnboardingWizard>
      {renderStepContent()}
    </OnboardingWizard>
  );
}

export default function OnboardingPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <span className="loading loading-spinner loading-lg mb-4"></span>
          <p className="text-lg font-medium text-gray-700">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect if not authenticated
  if (status === 'unauthenticated') {
    router.push('/auth/sign-in');
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-lg font-medium text-gray-700">Redirecting to sign-in...</p>
        </div>
      </div>
    );
  }

  // Check if user is INDEPENDENT_TEACHER
  if (session?.user?.role !== EUserRole.INDEPENDENT_TEACHER) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="alert alert-error">
            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>Access denied. Onboarding is only available to Independent Teachers.</span>
          </div>
        </div>
      </div>
    );
  }

  // Check if user already has a school (skip onboarding)
  if (session?.user?.schoolId) {
    router.push('/my-school');
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-lg font-medium text-gray-700">Redirecting to your school dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <OnboardingErrorBoundary>
      <OnboardingProvider>
        <OnboardingStepContent />
      </OnboardingProvider>
    </OnboardingErrorBoundary>
  );
}
