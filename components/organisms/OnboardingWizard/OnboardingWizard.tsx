'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useOnboarding } from './OnboardingContext';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';

interface OnboardingWizardProps {
  children: React.ReactNode;
}

export default function OnboardingWizard({ children }: OnboardingWizardProps) {
  const { state } = useOnboarding();
  const { steps, currentStep, error } = state;

  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const currentStepData = steps[currentStepIndex];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
      <div className="container mx-auto px-4 py-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          <div>
            {/* Header */}
          <div className="text-center mb-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <h1 className="text-2xl font-bold text-gray-800 mb-1">
                Welcome to EduSG!
              </h1>
              <p className="text-gray-600 text-sm">
                Let's set up your school and get you started
              </p>
            </motion.div>
          </div>

          {/* Progress Steps */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div className="steps w-full mb-4">
              {steps.map((step, index) => (
                <div
                  key={step.id}
                  className={`step ${
                    index <= currentStepIndex ? 'step-primary' : ''
                  }`}
                  data-content={step.isCompleted ? '✓' : index + 1}
                >
                  <div className="text-center">
                    <div className="font-medium text-sm">{step.title}</div>
                    <div className="text-xs text-gray-500 hidden sm:block">
                      {step.description}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Error Alert */}
          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              
            >
              <div className="mb-4">
              <AlertMessage
                type="error"
                message={error}
              />
              </div>
            </motion.div>
          )}

          {/* Step Content */}
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            
          >
            <div className="relative">
              <div className="mt-8">
                {children}
              </div>
            </div>
          </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
