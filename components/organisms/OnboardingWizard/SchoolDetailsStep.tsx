'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { ArrowRight } from 'lucide-react';
import { useOnboarding } from './OnboardingContext';
import { SchoolCreationForm } from '@/components/organisms/SchoolCreationForm';
import { Button } from '@/components/atoms/Button/Button';

export default function SchoolDetailsStep() {
  const router = useRouter();
  const { 
    state, 
    setSchoolData, 
    markStepCompleted, 
    setError, 
    setLoading,
    getNextStep 
  } = useOnboarding();
  const { isLoading } = state;

  const handleSchoolCreated = () => {
    markStepCompleted('school-details');

    // Navigate to next step
    const nextStep = getNextStep();
    if (nextStep) {
      router.push(`/onboarding/${nextStep}`);
    }
  };

  const handleSchoolCreationError = (error: string) => {
    setError(error);
  };

  const handleSkip = () => {
    // Allow skipping school creation for now, but mark as incomplete
    const nextStep = getNextStep();
    if (nextStep) {
      router.push(`/onboarding/${nextStep}`);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="space-y-4">
        {/* School Creation Form */}
        <SchoolCreationForm
          onSuccess={handleSchoolCreated}
          onError={handleSchoolCreationError}
        />

        {/* Skip Option */}
        <div className="flex justify-between items-center pt-3 border-t border-gray-200">
          <Button
            variant="ghost"
            onClick={handleSkip}
            disabled={isLoading}
            className="text-gray-600 hover:text-gray-800"
          >
            Skip for now
          </Button>

          <div className="text-sm text-gray-500">
            Complete later in dashboard
          </div>
        </div>
      </div>
    </motion.div>
  );
}
