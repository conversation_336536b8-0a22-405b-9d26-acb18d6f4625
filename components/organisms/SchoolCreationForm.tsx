'use client';

import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { handleCreateSchoolAction } from '@/actions/school.action';
import { createSchoolFormSchema, CreateSchoolFormData } from '@/lib/validators/school.validator';
import { Icon, Button } from '@/components/atoms';

// Props interface for the component
interface SchoolCreationFormProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const SchoolCreationForm: React.FC<SchoolCreationFormProps> = ({
  onSuccess,
  onError
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CreateSchoolFormData>({
    resolver: zodResolver(createSchoolFormSchema),
  });

  const onSubmit: SubmitHandler<CreateSchoolFormData> = async (data) => {
    setIsSubmitting(true);
    setSuccessMessage(null);
    setErrorMessage(null);

    try {
      // Call the server action with the form data
      const result = await handleCreateSchoolAction({
        name: data.name,
        address: data.address || '',
        phoneNumber: data.phoneNumber || '',
        registeredNumber: data.registeredNumber || '', // Required by API but not in task spec
        email: data.email || '',
      });

      if (result.status === 'success') {
        setSuccessMessage('School created successfully!');
        reset(); // Reset the form on success

        // Call onSuccess callback if provided
        if (onSuccess) {
          setTimeout(() => {
            onSuccess();
          }, 1500); // Give time to show success message
        }
      } else {
        // Handle error response
        const errorMsg = Array.isArray(result.message)
          ? result.message.map((m: any) =>
              typeof m === 'object' && m.message ? m.message : String(m)
            ).join(', ')
          : String(result.message || 'Failed to create school');
        setErrorMessage(errorMsg);

        // Call onError callback if provided
        if (onError) {
          onError(errorMsg);
        }
      }
    } catch (error: any) {
      console.error('Error creating school:', error);
      const errorMsg = error.message || 'An unexpected error occurred';
      setErrorMessage(errorMsg);

      // Call onError callback if provided
      if (onError) {
        onError(errorMsg);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto min-h-screen flex flex-col justify-center py-4">
      {/* Success Alert */}
      {successMessage && (
        <div role="alert" className="alert alert-success mb-6 shadow-lg">
          <Icon variant="check-circle" size={6} className="text-success" />
          <span className="font-medium">{successMessage}</span>
        </div>
      )}

      {/* Error Alert */}
      {errorMessage && (
        <div role="alert" className="alert alert-error mb-6 shadow-lg">
          <Icon variant="x-circle" size={6} className="text-error" />
          <span className="font-medium">{errorMessage}</span>
        </div>
      )}

      {/* Form Card */}
      <div className="card bg-base-100 shadow-2xl shadow-blue-100/20">
        <div className="card-body p-6">
          <h2 className="card-title text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
            <Icon variant="building-2" size={6} className="text-primary" />
            School Details
          </h2>

          <form onSubmit={handleSubmit(onSubmit)} className="w-full space-y-5">{/* Form */}

            {/* Basic Information Section */}
            <div className="space-y-4">
              <h3 className="text-base font-semibold text-gray-700 border-b border-gray-200 pb-1">
                Basic Information
              </h3>
              {/* School Name Field */}
              <div className="form-control">
                <label className="label py-1" htmlFor="name">
                  <span className="label-text text-sm font-semibold text-gray-700">
                    School Name
                    <span className="text-xs font-normal text-gray-500 ml-1">(required)</span>
                  </span>
                </label>
                <label className={`input input-bordered flex items-center gap-3 h-11 rounded-lg transition-all duration-300 ${
                  errors.name
                    ? 'input-error border-red-400 focus-within:border-red-500 focus-within:ring-2 focus-within:ring-red-100'
                    : 'border-gray-200 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-100 hover:border-blue-300'
                }`}>
                  <Icon variant="building-2" size={4} className="text-gray-500" />
                  <input
                    id="name"
                    type="text"
                    placeholder="Lincoln High School"
                    className="grow text-sm bg-transparent"
                    {...register('name')}
                    disabled={isSubmitting}
                  />
                </label>
                {errors.name && (
                  <div className="label py-1">
                    <span className="label-text-alt text-error font-medium text-xs">
                      {errors.name.message}
                    </span>
                  </div>
                )}
              </div>

              {/* School Address Field */}
              <div className="form-control">
                <label className="label py-1" htmlFor="address">
                  <span className="label-text text-sm font-semibold text-gray-700">School Address</span>
                </label>
                <label className={`input input-bordered flex items-center gap-3 h-11 rounded-lg transition-all duration-300 ${
                  errors.address
                    ? 'input-error border-red-400 focus-within:border-red-500 focus-within:ring-2 focus-within:ring-red-100'
                    : 'border-gray-200 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-100 hover:border-blue-300'
                }`}>
                  <Icon variant="map-pin" size={4} className="text-gray-500" />
                  <input
                    id="address"
                    type="text"
                    placeholder="123 Main Street, City, State"
                    className="grow text-sm bg-transparent"
                    {...register('address')}
                    disabled={isSubmitting}
                  />
                </label>
                {errors.address && (
                  <div className="label py-1">
                    <span className="label-text-alt text-error font-medium text-xs">
                      {errors.address.message}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Contact Information Section */}
            <div className="space-y-4">
              <h3 className="text-base font-semibold text-gray-700 border-b border-gray-200 pb-1">
                Contact Information
              </h3>

              {/* School Phone Field */}
              <div className="form-control">
                <label className="label py-1" htmlFor="phoneNumber">
                  <span className="label-text text-sm font-semibold text-gray-700">School Phone</span>
                </label>
                <label className={`input input-bordered flex items-center gap-3 h-11 rounded-lg transition-all duration-300 ${
                  errors.phoneNumber
                    ? 'input-error border-red-400 focus-within:border-red-500 focus-within:ring-2 focus-within:ring-red-100'
                    : 'border-gray-200 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-100 hover:border-blue-300'
                }`}>
                  <Icon variant="phone" size={4} className="text-gray-500" />
                  <input
                    id="phoneNumber"
                    type="tel"
                    placeholder="(*************"
                    className="grow text-sm bg-transparent"
                    {...register('phoneNumber')}
                    disabled={isSubmitting}
                  />
                </label>
                {errors.phoneNumber && (
                  <div className="label py-1">
                    <span className="label-text-alt text-error font-medium text-xs">
                      {errors.phoneNumber.message}
                    </span>
                  </div>
                )}
              </div>

              {/* School Email Field */}
              <div className="form-control">
                <label className="label py-1" htmlFor="email">
                  <span className="label-text text-sm font-semibold text-gray-700">School Email</span>
                </label>
                <label className={`input input-bordered flex items-center gap-3 h-11 rounded-lg transition-all duration-300 ${
                  errors.email
                    ? 'input-error border-red-400 focus-within:border-red-500 focus-within:ring-2 focus-within:ring-red-100'
                    : 'border-gray-200 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-100 hover:border-blue-300'
                }`}>
                  <Icon variant="mail" size={4} className="text-gray-500" />
                  <input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    className="grow text-sm bg-transparent"
                    {...register('email')}
                    disabled={isSubmitting}
                  />
                </label>
                {errors.email && (
                  <div className="label py-1">
                    <span className="label-text-alt text-error font-medium text-xs">
                      {errors.email.message}
                    </span>
                  </div>
                )}
              </div>
            </div>

              {/* Submit Button */}
              <div className="form-control mt-6 flex justify-center">
                <Button
                  type="submit"
                  variant="primary"
                  className="w-auto px-8 h-12 text-base font-semibold rounded-lg bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 border-0 shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 focus:ring-4 focus:ring-blue-200 focus:ring-offset-2 disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none"
                  isLoading={isSubmitting}
                  disabled={isSubmitting}
                  iconProps={{
                    variant: "building-2",
                    size: 4,
                    className: "text-white"
                  }}
                >
                  {isSubmitting ? 'Creating School...' : 'Create School'}
                </Button>
              </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default SchoolCreationForm;
